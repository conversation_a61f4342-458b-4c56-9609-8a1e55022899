package com.jerome.supernuggetsmaster

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.fragment.app.FragmentActivity
import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.BugReport
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.jerome.supernuggetsmaster.permission.PermissionManager
import com.jerome.supernuggetsmaster.ui.ScriptViewModel
import com.jerome.supernuggetsmaster.ui.MijiaBottomActionBar
import com.jerome.supernuggetsmaster.ui.MijiaScriptListScreen
import com.jerome.supernuggetsmaster.ui.PermissionSetupScreen
import com.jerome.supernuggetsmaster.ui.theme.SuperNuggetsMasterTheme
import com.jerome.supernuggetsmaster.utils.OpenCVInitializer
import com.jerome.supernuggetsmaster.utils.MediaProjectionHelper
import com.jerome.supernuggetsmaster.utils.GlobalMediaProjectionLauncher
import com.jerome.supernuggetsmaster.utils.PermissionStatusMonitor
import com.jerome.supernuggetsmaster.float_window.FloatWindowManager
import com.jerome.supernuggetsmaster.utils.Logger
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : FragmentActivity() {

    @Inject
    lateinit var permissionManager: PermissionManager

    @Inject
    lateinit var openCVInitializer: OpenCVInitializer

    @Inject
    lateinit var mediaProjectionHelper: MediaProjectionHelper

    @Inject
    lateinit var permissionStatusMonitor: PermissionStatusMonitor

    @Inject
    lateinit var imageRecognitionHelper: com.jerome.supernuggetsmaster.utils.ImageRecognitionHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 初始化OpenCV
        openCVInitializer.initialize { success ->
            if (success) {
                Logger.i("OpenCV初始化成功，图像识别功能可用")
                // 同步设置ImageRecognitionHelper的状态
                imageRecognitionHelper.setOpenCVInitialized(true)
            } else {
                Logger.w("OpenCV初始化失败，图像识别功能不可用")
                // 同步设置ImageRecognitionHelper的状态
                imageRecognitionHelper.setOpenCVInitialized(false)
            }
        }

        // 初始化全局媒体投影启动器
        GlobalMediaProjectionLauncher.initialize(this, mediaProjectionHelper)

        // 启动权限状态监听
        permissionStatusMonitor.startMonitoring()

        setContent {
            SuperNuggetsMasterTheme {
                MainScreen(
                    permissionManager = permissionManager,
                    mediaProjectionHelper = mediaProjectionHelper,
                    activity = this@MainActivity
                )
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 每次回到前台时检查权限状态
        permissionManager.checkAllPermissions()

        // 如果权限状态监听器还没启动，启动它
        if (!::permissionStatusMonitor.isInitialized) {
            permissionStatusMonitor.startMonitoring()
        } else {
            // 立即检查一次权限状态
            permissionStatusMonitor.checkNow()
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理悬浮窗资源，防止内存泄漏
        FloatWindowManager.destroy()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    permissionManager: PermissionManager,
    mediaProjectionHelper: MediaProjectionHelper,
    activity: FragmentActivity
) {
    val scriptViewModel: ScriptViewModel = hiltViewModel()
    val permissionStatus by permissionManager.permissionStatus.collectAsStateWithLifecycle()
    val scripts by scriptViewModel.scripts.collectAsStateWithLifecycle()
    val executionStatus by scriptViewModel.executionStatus.collectAsStateWithLifecycle()
    val currentScript by scriptViewModel.currentScript.collectAsStateWithLifecycle()
    val executionProgress by scriptViewModel.executionProgress.collectAsStateWithLifecycle()
    val selectedScriptCount by scriptViewModel.selectedScriptCount.collectAsStateWithLifecycle()

    // 启动时检查权限
    LaunchedEffect(Unit) {
        permissionManager.checkAllPermissions()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "SuperNuggets Master",
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                },
                actions = {
                    IconButton(
                        onClick = {
                            FloatWindowManager.show(activity)
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.BugReport,
                            contentDescription = "调试模式",
                            tint = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            )
        },
        bottomBar = {
            if (permissionStatus.allPermissionsGranted) {
                MijiaBottomActionBar(
                    selectedCount = selectedScriptCount,
                    executionStatus = executionStatus,
                    onStartExecution = { scriptViewModel.startExecution(activity) },
                    onStopExecution = { scriptViewModel.stopExecution() },
                    onSelectAll = { scriptViewModel.toggleSelectAll() },
                    onReset = { scriptViewModel.resetExecution() }
                )
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            if (!permissionStatus.allPermissionsGranted) {
                // 权限未完成时显示权限页面
                PermissionSetupScreen(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(innerPadding),
                    permissionStatus = permissionStatus,
                    permissionManager = permissionManager,
                    mediaProjectionHelper = mediaProjectionHelper,
                    activity = activity
                )
            } else {
                // 权限完成后显示脚本列表
                MijiaScriptListScreen(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(innerPadding),
                    scripts = scripts,
                    executionStatus = executionStatus,
                    currentScript = currentScript,
                    executionProgress = executionProgress,
                    onScriptToggle = { scriptId -> scriptViewModel.toggleScriptSelection(scriptId) }
                )
            }
        }
    }
}
