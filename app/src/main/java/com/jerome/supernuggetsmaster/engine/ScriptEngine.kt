package com.jerome.supernuggetsmaster.engine

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import com.jerome.supernuggetsmaster.annotation.ScriptModel
import com.jerome.supernuggetsmaster.annotation.ScriptPage
import com.jerome.supernuggetsmaster.data.*
import com.jerome.supernuggetsmaster.utils.Logger
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.lang.reflect.Method
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.reflect.full.callSuspend
import kotlin.reflect.jvm.kotlinFunction

/**
 * 脚本引擎 - 核心执行引擎
 * 负责脚本加载、页面识别、逻辑执行等核心功能
 */
@Singleton
class ScriptEngine @Inject constructor(
    private val context: Context,
    private val pageRecognizer: PageRecognizer,
    private val appLauncher: AppLauncher
) {
    
    // 已加载的脚本列表
    private val _loadedScripts = MutableStateFlow<List<ScriptInfo>>(emptyList())
    val loadedScripts: StateFlow<List<ScriptInfo>> = _loadedScripts.asStateFlow()
    
    // 当前执行状态
    private val _executionStatus = MutableStateFlow(ScriptExecutionStatus.IDLE)
    val executionStatus: StateFlow<ScriptExecutionStatus> = _executionStatus.asStateFlow()
    
    // 当前执行上下文
    private val _currentContext = MutableStateFlow<ScriptExecutionContext?>(null)
    val currentContext: StateFlow<ScriptExecutionContext?> = _currentContext.asStateFlow()
    
    // 执行进度
    private val _executionProgress = MutableStateFlow(0f)
    val executionProgress: StateFlow<Float> = _executionProgress.asStateFlow()
    
    // 执行结果列表
    private val _executionResults = MutableStateFlow<List<ScriptExecutionResult>>(emptyList())
    val executionResults: StateFlow<List<ScriptExecutionResult>> = _executionResults.asStateFlow()
    
    private var isRunning = false
    
    /**
     * 初始化引擎，扫描并加载所有脚本
     */
    suspend fun initialize() {
        Logger.i("开始初始化脚本引擎")
        try {
            val scripts = scanAndLoadScripts()
            _loadedScripts.value = scripts
            Logger.i("成功加载 ${scripts.size} 个脚本")
        } catch (e: Exception) {
            Logger.e("脚本引擎初始化失败", e)
        }
    }
    
    /**
     * 扫描并加载所有脚本类
     */
    private suspend fun scanAndLoadScripts(): List<ScriptInfo> {
        val scripts = mutableListOf<ScriptInfo>()
        
        // 手动注册脚本类（在实际项目中可以通过包扫描自动发现）
        val scriptClasses = listOf(
            com.jerome.supernuggetsmaster.scripts.WeChatScript::class.java,
            com.jerome.supernuggetsmaster.scripts.KuaishouScript::class.java,
            com.jerome.supernuggetsmaster.scripts.AlipayScript::class.java
        )
        
        for (scriptClass in scriptClasses) {
            try {
                val scriptInfo = parseScriptClass(scriptClass)
                if (scriptInfo != null) {
                    scripts.add(scriptInfo)
                    Logger.d("成功加载脚本: ${scriptInfo.name}")
                }
            } catch (e: Exception) {
                Logger.e("加载脚本失败: ${scriptClass.simpleName}", e)
            }
        }
        
        return scripts
    }
    
    /**
     * 解析脚本类，提取注解信息
     */
    private fun parseScriptClass(scriptClass: Class<*>): ScriptInfo? {
        val scriptModel = scriptClass.getAnnotation(ScriptModel::class.java) ?: return null
        
        return ScriptInfo(
            id = scriptModel.id,
            name = scriptModel.name,
            description = scriptModel.description,
            targetApp = scriptModel.targetApp,
            icon = scriptModel.icon,
            estimatedDuration = scriptModel.estimatedDuration,
            scriptClass = scriptClass
        )
    }
    
    /**
     * 解析脚本类的页面方法
     */
    private fun parseScriptPages(scriptClass: Class<*>): List<ScriptPageInfo> {
        val pages = mutableListOf<ScriptPageInfo>()
        
        for (method in scriptClass.declaredMethods) {
            val pageAnnotation = method.getAnnotation(ScriptPage::class.java)
            if (pageAnnotation != null) {
                
                val pageInfo = ScriptPageInfo(
                    name = pageAnnotation.name,
                    ids = pageAnnotation.ids.toList(),
                    tempImgs = pageAnnotation.tempImgs.toList(),
                    method = method
                )
                pages.add(pageInfo)
            }
        }
        
        return pages
    }

    /**
     * 开始执行选中的脚本
     */
    suspend fun executeSelectedScripts() {
        if (isRunning) {
            Logger.w("脚本引擎正在运行中")
            return
        }
        
        val selectedScripts = _loadedScripts.value.filter { it.isSelected }
        if (selectedScripts.isEmpty()) {
            Logger.w("没有选中的脚本")
            return
        }
        
        isRunning = true
        _executionStatus.value = ScriptExecutionStatus.PREPARING
        _executionResults.value = emptyList()
        
        Logger.i("开始执行 ${selectedScripts.size} 个脚本")
        
        try {
            selectedScripts.forEachIndexed { index, scriptInfo ->
                Logger.i("开始执行脚本: ${scriptInfo.name}")
                
                val result = executeScript(scriptInfo)
                
                // 添加执行结果
                val currentResults = _executionResults.value.toMutableList()
                currentResults.add(result)
                _executionResults.value = currentResults
                
                // 更新进度
                _executionProgress.value = (index + 1).toFloat() / selectedScripts.size
                
                Logger.i("脚本执行完成: ${scriptInfo.name}, 状态: ${result.status}")
                
                // 脚本间延迟
                if (index < selectedScripts.size - 1) {
                    delay(2000)
                }
            }
            
            _executionStatus.value = ScriptExecutionStatus.COMPLETED
            Logger.i("所有脚本执行完成")
            
        } catch (e: Exception) {
            _executionStatus.value = ScriptExecutionStatus.FAILED
            Logger.e("脚本执行过程中发生错误", e)
        } finally {
            isRunning = false
            _currentContext.value = null
        }
    }
    
    /**
     * 执行单个脚本
     */
    private suspend fun executeScript(scriptInfo: ScriptInfo): ScriptExecutionResult {
        val startTime = System.currentTimeMillis()
        
        try {
            _executionStatus.value = ScriptExecutionStatus.RUNNING
            
            // 创建脚本实例
            val scriptInstance = scriptInfo.scriptClass.getDeclaredConstructor().newInstance()
            
            // 创建执行上下文
            val context = ScriptExecutionContext(
                scriptInfo = scriptInfo,
                currentPage = null,
                scriptInstance = scriptInstance,
                isAppLaunched = false
            )
            _currentContext.value = context
            
            // 1. 检查并启动目标应用
            val appLaunched = appLauncher.launchApp(scriptInfo.targetApp)
            if (!appLaunched) {
                return ScriptExecutionResult(
                    scriptId = scriptInfo.id,
                    status = ScriptExecutionStatus.FAILED,
                    message = "无法启动目标应用: ${scriptInfo.targetApp}",
                    executionTime = System.currentTimeMillis() - startTime
                )
            }
            
            // 等待应用启动
            delay(3000)
            
            // 2. 初始化图像识别组件
            var imageRecognitionReady = false
            pageRecognizer.initImageRecognition { success ->
                imageRecognitionReady = success
                if (success) {
                    Logger.i("图像识别初始化成功，开始页面识别")
                } else {
                    Logger.w("图像识别初始化失败，将仅使用ID识别")
                }
            }
            
            // 等待图像识别初始化完成
            var waitTime = 0
            while (!imageRecognitionReady && waitTime < 5000) {
                delay(100)
                waitTime += 100
            }
            
            // 3. 获取脚本页面信息
            val pages = parseScriptPages(scriptInfo.scriptClass)
            if (pages.isEmpty()) {
                return ScriptExecutionResult(
                    scriptId = scriptInfo.id,
                    status = ScriptExecutionStatus.FAILED,
                    message = "脚本中没有定义页面方法",
                    executionTime = System.currentTimeMillis() - startTime
                )
            }
            
            // 4. 执行脚本逻辑循环
            var maxIterations = 20  // 防止无限循环
            var currentIteration = 0
            
            while (currentIteration < maxIterations && isRunning) {
                currentIteration++
                
                // 识别当前页面
                val recognitionResult = pageRecognizer.recognizeCurrentPage(pages)
                
                if (recognitionResult.pageInfo != null) {
                    Logger.d("识别到页面: ${recognitionResult.pageInfo.name}")
                    
                    // 更新执行上下文
                    _currentContext.value = context.copy(currentPage = recognitionResult.pageInfo)
                    
                    // 执行页面逻辑
                    val pageExecuted = executePageLogic(
                        scriptInstance, 
                        recognitionResult.pageInfo
                    )
                    
                    if (!pageExecuted) {
                        Logger.w("页面逻辑执行失败: ${recognitionResult.pageInfo.name}")
                        break
                    }
                    
                    // 等待页面变化
                    delay(2000)
                } else {
                    Logger.w("无法识别当前页面，尝试继续")
                    delay(1000)
                }
            }
            
            return ScriptExecutionResult(
                scriptId = scriptInfo.id,
                status = ScriptExecutionStatus.COMPLETED,
                message = "脚本执行完成，共执行 $currentIteration 次页面逻辑",
                executionTime = System.currentTimeMillis() - startTime
            )
            
        } catch (e: Exception) {
            Logger.e("执行脚本时发生异常: ${scriptInfo.name}", e)
            return ScriptExecutionResult(
                scriptId = scriptInfo.id,
                status = ScriptExecutionStatus.FAILED,
                message = "执行异常: ${e.message}",
                executionTime = System.currentTimeMillis() - startTime
            )
        }
    }
    
    /**
     * 执行页面逻辑
     */
    private suspend fun executePageLogic(
        scriptInstance: Any,
        pageInfo: ScriptPageInfo
    ): Boolean {
        return try {
            // 调用页面方法
            pageInfo.method.isAccessible = true
            
            // 使用Kotlin反射API来调用方法
            val kotlinFunction = pageInfo.method.kotlinFunction
            if (kotlinFunction != null && kotlinFunction.isSuspend) {
                // 调用suspend函数，使用callSuspend方法
                kotlinFunction.callSuspend(scriptInstance)
            } else {
                // 调用普通函数
                pageInfo.method.invoke(scriptInstance)
            }
            true
        } catch (e: Exception) {
            Logger.e("执行页面逻辑失败: ${pageInfo.name}", e)
            false
        }
    }
    
    /**
     * 停止脚本执行
     */
    fun stopExecution() {
        Logger.i("停止脚本执行")
        isRunning = false
        _executionStatus.value = ScriptExecutionStatus.STOPPED
        _currentContext.value = null
    }
    
    /**
     * 重置执行状态
     */
    fun resetExecution() {
        isRunning = false
        _executionStatus.value = ScriptExecutionStatus.IDLE
        _executionProgress.value = 0f
        _currentContext.value = null
        _executionResults.value = emptyList()
        Logger.d("重置脚本引擎状态")
    }
    
    /**
     * 切换脚本选择状态
     */
    fun toggleScriptSelection(scriptId: String) {
        val currentScripts = _loadedScripts.value.toMutableList()
        val index = currentScripts.indexOfFirst { it.id == scriptId }
        if (index != -1) {
            currentScripts[index] = currentScripts[index].copy(isSelected = !currentScripts[index].isSelected)
            _loadedScripts.value = currentScripts
            Logger.d("切换脚本选择状态: $scriptId -> ${currentScripts[index].isSelected}")
        }
    }
    
    /**
     * 全选/取消全选
     */
    fun toggleSelectAll() {
        val currentScripts = _loadedScripts.value
        val allSelected = currentScripts.all { it.isSelected }
        
        val updatedScripts = currentScripts.map { script ->
            script.copy(isSelected = !allSelected)
        }
        _loadedScripts.value = updatedScripts
        Logger.d("全选/取消全选: ${!allSelected}")
    }
} 