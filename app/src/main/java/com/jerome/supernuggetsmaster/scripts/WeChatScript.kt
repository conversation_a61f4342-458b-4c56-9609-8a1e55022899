package com.jerome.supernuggetsmaster.scripts

import android.view.accessibility.AccessibilityNodeInfo
import com.jerome.supernuggetsmaster.annotation.ScriptModel
import com.jerome.supernuggetsmaster.annotation.ScriptPage
import com.jerome.supernuggetsmaster.utils.AssistHelper
import com.jerome.supernuggetsmaster.utils.AssistHelper.clickByCoordinatesInternal
import com.jerome.supernuggetsmaster.utils.Logger
import kotlinx.coroutines.delay

@ScriptModel(
    id = "wechat",
    name = "暖暖孕迹",
    description = "获取免费使用时长",
    targetApp = "com.tencent.mm",
    icon = "💬",
    estimatedDuration = 240 // 秒为单位
)
class WeChatScript {

    @ScriptPage(
        name = "微信首页",
        tempImgs = ["wechat/page_main.png"],
    )
    suspend fun mainPage() {
        //com.tencent.mm:id/huj 是微信底部tab的id,它的4个子节点分别是 微信，通讯录，发现，我
        //要求通过下标进行这四个节点的定位

        //业务逻辑是 先点击 通讯录 等待3秒 再点击 我 等待3秒 再点击 发现 等待3秒，再点击微信 等待3秒，最后进行一个下拉操作

        try {
            // 等待底部导航栏加载
            delay(1000)

            // 检查底部tab容器是否存在
            val tabContainers = AssistHelper.findById("com.tencent.mm:id/nvt")
            if (tabContainers.isNullOrEmpty()) {
                Logger.e("未找到微信底部tab容器")
                return
            }

            // 1. 点击通讯录（下标1）
            Logger.i("步骤1: 点击通讯录")
            if (AssistHelper.clickNode(tabContainers[1])) {
                Logger.i("通讯录点击成功")
                delay(1000) // 等待3秒
            } else {
                Logger.e("通讯录点击失败")
            }

            // 2. 点击我（下标3）
            Logger.i("步骤2: 点击我")
            if (AssistHelper.clickNode(tabContainers[3])) {
                Logger.i("我点击成功")
                delay(1000) // 等待3秒
            } else {
                Logger.e("我点击失败")
            }

            // 3. 点击发现（下标2）
            Logger.i("步骤3: 点击发现")
            if (AssistHelper.clickNode(tabContainers[2])) {
                Logger.i("发现点击成功")
                delay(1000) // 等待3秒
            } else {
                Logger.e("发现点击失败")
            }

            // 4. 点击微信（下标0）
            Logger.i("步骤4: 点击微信")
            if (AssistHelper.clickNode(tabContainers[0])) {
                Logger.i("微信点击成功")
                delay(1000) // 等待3秒
            } else {
                Logger.e("微信点击失败")
            }

            // 5.点击搜索
            val sarchBtn = AssistHelper.findByDesc("搜索")
            if (sarchBtn != null) {
                Logger.i("步骤5: 查找搜索成功,点击搜索")
                AssistHelper.clickNode(sarchBtn)
                delay(3000)
            } else {
                Logger.i("步骤5: 查找搜索失败")
            }
        } catch (e: Exception) {
            Logger.e("执行微信首页操作时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }

    @ScriptPage(
        name = "小程序搜索页",
        tempImgs = ["wechat/page_search.png"],
    )
    suspend fun minSearchPage() {
        try {
            Logger.i("开始执行小程序搜索页操作")

            // 等待页面加载
            delay(2000)

            // 方法1：通过文本查找搜索按钮
            Logger.i("尝试通过文本查找搜索按钮")
            val searchButton = AssistHelper.findByText("搜索")
            if (searchButton != null) {
                Logger.i("找到搜索按钮")
                if (AssistHelper.clickNode(searchButton)) {
                    Logger.i("搜索按钮点击成功，等待输入框激活")
                    delay(1000) // 等待输入框激活

                    // 输入文本"暖暖孕迹"
                    val inputSuccess = AssistHelper.inputText(searchButton, "暖暖孕迹")
                    if (inputSuccess) {
                        Logger.i("文本输入成功: 暖暖孕迹")
                        delay(3000)
                        AssistHelper.getImageRecognitionRect("wechat/item_luanluan.png")
                            ?.let { rect ->
                                clickByCoordinatesInternal(rect.centerX, rect.centerY)
                                delay(3000)
                            }
                    } else {
                        Logger.e("文本输入失败")
                    }
                    delay(2000)
                } else {
                    Logger.e("搜索按钮点击失败")
                }
            } else {
                Logger.w("未找到搜索按钮，尝试其他方法")
            }
        } catch (e: Exception) {
            Logger.e("执行小程序搜索页操作时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }
}