package com.jerome.supernuggetsmaster.permission

import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.view.accessibility.AccessibilityManager
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton
import com.jerome.supernuggetsmaster.utils.Logger

/**
 * 权限管理器
 * 
 * 职责：
 * 1. 检查各种权限状态
 * 2. 引导用户开启权限
 * 3. 监听权限变化
 */
@Singleton
class PermissionManager @Inject constructor(
    @ApplicationContext private val context: Context
) {

    // 权限状态数据类
    data class PermissionStatus(
        val selectToSpeakEnabled: Boolean = false,
        val overlayEnabled: Boolean = false,
        val mediaProjectionEnabled: Boolean = false,
        val allPermissionsGranted: Boolean = false
    )

    // 权限状态流
    private val _permissionStatus = MutableStateFlow(PermissionStatus())
    val permissionStatus: StateFlow<PermissionStatus> = _permissionStatus.asStateFlow()

    /**
     * 检查所有权限状态
     */
    fun checkAllPermissions() {
        val selectToSpeakEnabled = isSelectToSpeakServiceEnabled()
        val overlayEnabled = canDrawOverlays()
        val mediaProjectionEnabled = isMediaProjectionEnabled()

        _permissionStatus.value = PermissionStatus(
            selectToSpeakEnabled = selectToSpeakEnabled,
            overlayEnabled = overlayEnabled,
            mediaProjectionEnabled = mediaProjectionEnabled,
            allPermissionsGranted = selectToSpeakEnabled && overlayEnabled && mediaProjectionEnabled
        )
    }

    /**
     * 检查SelectToSpeak伪装服务是否已启用
     */
    fun isSelectToSpeakServiceEnabled(): Boolean {
        val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
        val enabledServices = accessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK)
        
        val packageName = context.packageName
        val serviceName = "com.google.android.accessibility.selecttospeak.SelectToSpeakService"
        val fullServiceName = "$packageName/$serviceName"
        
        return enabledServices.any { serviceInfo ->
            val serviceId = serviceInfo?.resolveInfo?.serviceInfo?.let { info ->
                "${info.packageName}/${info.name}"
            }
            serviceId == fullServiceName
        }
    }

    /**
     * 检查悬浮窗权限
     */
    fun canDrawOverlays(): Boolean {
        return Settings.canDrawOverlays(context)
    }

    /**
     * 跳转到无障碍服务设置页面
     */
    fun requestAccessibilityPermission() {
        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开无障碍设置页面，尝试打开通用设置
            openGeneralSettings()
        }
    }

    /**
     * 跳转到悬浮窗权限设置页面
     */
    fun requestOverlayPermission() {
        try {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                data = Uri.parse("package:${context.packageName}")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果无法打开悬浮窗设置页面，尝试打开应用详情页
            openAppDetails()
        }
    }

    /**
     * 打开应用详情页
     */
    fun openAppDetails() {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:${context.packageName}")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            openGeneralSettings()
        }
    }

    /**
     * 打开通用设置页面
     */
    private fun openGeneralSettings() {
        try {
            val intent = Intent(Settings.ACTION_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            // 最后的兜底方案，如果连设置都打不开就没办法了
        }
    }

    /**
     * 检查媒体投影权限状态
     */
    fun isMediaProjectionEnabled(): Boolean {
        // 媒体投影权限需要在运行时动态获取，这里返回是否已经初始化
        return MediaProjectionManager.isInitialized()
    }
}

/**
 * 媒体投影管理器
 * 单独管理媒体投影的初始化状态
 */
object MediaProjectionManager {
    private var initialized = false
    
    // 添加时间戳来跟踪最后设置时间，避免状态抖动
    private var lastSetTime = 0L

    fun setInitialized(value: Boolean) {
        val currentTime = System.currentTimeMillis()
        
        // 避免短时间内频繁状态变化
        if (value != initialized || currentTime - lastSetTime > 1000) {
            initialized = value
            lastSetTime = currentTime
            Logger.d("MediaProjectionManager状态更新: initialized = $value")
        }
    }

    fun isInitialized(): Boolean = initialized
}