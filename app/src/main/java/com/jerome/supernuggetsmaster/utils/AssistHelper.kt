package com.jerome.supernuggetsmaster.utils

import android.graphics.Rect
import android.os.Bundle
import android.view.accessibility.AccessibilityNodeInfo
import com.google.android.accessibility.selecttospeak.SelectToSpeakService
import com.jerome.supernuggetsmaster.SuperNuggetsMasterApplication
import kotlinx.coroutines.delay

/**
 * 自动化辅助工具类
 * 为脚本提供简洁的API，封装无障碍服务的复杂操作
 *
 * 针对微信8.0.52+版本节点混淆问题的解决方案：
 * - 检测当前应用包名
 * - 在微信中优先使用SelectToSpeak伪装服务
 * - 其他应用继续使用原有的SelectToSpeakService
 */
object AssistHelper {

    // 微信包名
    private const val WECHAT_PACKAGE_NAME = "com.tencent.mm"

    /**
     * 获取适合当前应用的无障碍服务
     * 微信中使用SelectToSpeak服务，其他应用使用SelectToSpeakService
     */
    private fun getAppropriateService(): Any? {
        val currentPackage = getCurrentPackage()
        return if (currentPackage == WECHAT_PACKAGE_NAME) {
            // 微信中优先使用SelectToSpeak服务来绕过节点混淆
            SelectToSpeakService.getInstance() ?: SelectToSpeakService.getInstance()
        } else {
            // 其他应用使用原有服务
            SelectToSpeakService.getInstance()
        }
    }

    /**
     * 获取当前应用包名
     */
    private fun getCurrentPackage(): String? {
        return try {
            val rootNode = SelectToSpeakService.getInstance()?.getRootNode()
                ?: SelectToSpeakService.getInstance()?.getRootNode()
            rootNode?.packageName?.toString()
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 统一的节点查找接口 - 根据ID查找
     */
    private fun findNodeByIdInternal(id: String): List<AccessibilityNodeInfo?>? {
        val service = getAppropriateService()
        return when (service) {
            is SelectToSpeakService -> service.findNodeById(id)
            else -> null
        }
    }

    /**
     * 统一的坐标点击接口
     */
    fun clickByCoordinatesInternal(x: Float, y: Float): Boolean {
        // 使用SelectToSpeakService进行坐标点击
        return SelectToSpeakService.getInstance()?.clickByCoordinates(x, y) ?: false
    }

    /**
     * 统一的坐标长按接口
     * @param x 长按的X坐标
     * @param y 长按的Y坐标
     * @param durationMs 长按持续时间，默认1000毫秒
     */
    fun longClickByCoordinatesInternal(x: Float, y: Float, durationMs: Long = 1000): Boolean {
        // 使用SelectToSpeakService进行坐标长按
        return SelectToSpeakService.getInstance()?.longClickByCoordinates(x, y, durationMs) ?: false
    }

    private fun swipeDownInternal(): Boolean {
        // 使用手势滑动代替无障碍滚动
        val service = getAppropriateService()
        return when (service) {
            is SelectToSpeakService -> service.performGestureScroll(SelectToSpeakService.ScrollDirection.DOWN)
            else -> false
        }
    }

    private fun swipeUpInternal(): Boolean {
        val service = getAppropriateService()
        return when (service) {
            is SelectToSpeakService -> service.performGestureScroll(SelectToSpeakService.ScrollDirection.UP)
            else -> false
        }
    }

    private fun swipeLeftInternal(): Boolean {
        val service = getAppropriateService()
        return when (service) {
            is SelectToSpeakService -> service.performGestureScroll(SelectToSpeakService.ScrollDirection.LEFT)
            else -> false
        }
    }

    private fun swipeRightInternal(): Boolean {
        val service = getAppropriateService()
        return when (service) {
            is SelectToSpeakService -> service.performGestureScroll(SelectToSpeakService.ScrollDirection.RIGHT)
            else -> false
        }
    }

    /**
     * 通过ID查找元素
     * @param id 要查找的资源ID
     * @return 可点击的元素，如果未找到则返回空元素
     */
    fun findById(id: String): List<AccessibilityNodeInfo?>? {
        return findNodeByIdInternal(id)
    }

    /**
     * 通过内容描述查找元素
     * @param desc 要查找的内容描述
     * @return 找到的节点，如果未找到则返回null
     */
    fun findByDesc(desc: String): AccessibilityNodeInfo? {
        return try {
            val service = getAppropriateService()
            when (service) {
                is SelectToSpeakService -> {
                    val rootNode = service.getRootNode()
                    if (rootNode == null) {
                        Logger.w("根节点为空，无法查找描述: $desc")
                        return null
                    }

                    Logger.d("开始查找内容描述: $desc")
                    val node = findNodeByDescription(rootNode, desc, false)
                    if (node != null) {
                        Logger.d("找到匹配的节点: ${node.className}")
                    } else {
                        Logger.w("未找到包含描述 '$desc' 的节点")
                    }
                    node
                }

                else -> {
                    Logger.w("无法获取无障碍服务实例")
                    null
                }
            }
        } catch (e: Exception) {
            Logger.e("通过描述查找节点失败: $desc", e)
            null
        }
    }

    /**
     * 通过文本内容查找元素
     * @param text 要查找的文本内容
     * @return 找到的节点，如果未找到则返回null
     */
    fun findByText(text: String): AccessibilityNodeInfo? {
        return try {
            val service = getAppropriateService()
            when (service) {
                is SelectToSpeakService -> {
                    Logger.d("开始查找文本: $text")
                    val rootNode = service.getRootNode()
                    if (rootNode == null) {
                        Logger.w("根节点为空，无法查找描述: $text")
                        return null
                    }
                    val node = findNodeByDescription(rootNode, text, true)
                    if (node != null) {
                        Logger.d("找到匹配的节点: ${node.className}, 文本: ${node.text}")
                    } else {
                        Logger.w("未找到包含文本 '$text' 的节点")
                    }
                    node
                }

                else -> {
                    Logger.w("无法获取无障碍服务实例")
                    null
                }
            }
        } catch (e: Exception) {
            Logger.e("通过文本查找节点失败: $text", e)
            null
        }
    }

    /**
     * 递归查找包含指定描述的节点
     */
    private fun findNodeByDescription(
        node: AccessibilityNodeInfo,
        text: String,
        isText: Boolean
    ): AccessibilityNodeInfo? {
        try {
            // 检查当前节点的内容描述
            if (!isText && node.contentDescription?.toString()
                    ?.contains(text, ignoreCase = true) == true
            ) {
                Logger.d("找到匹配描述的节点: ${node.contentDescription}")
                return node
            }

            // 检查当前节点的文本内容
            if (isText && node.text?.toString()?.contains(text, ignoreCase = true) == true) {
                Logger.d("找到匹配文本的节点: ${node.text}")
                return node
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                child?.let { childNode ->
                    val result = findNodeByDescription(childNode, text, isText)
                    if (result != null) {
                        return result
                    }
                }
            }

            return null
        } catch (e: Exception) {
            Logger.e("递归查找节点描述失败", e)
            return null
        }
    }

    /**
     * 向下滑动
     * @return 是否滑动成功
     */
    suspend fun scrollDown(): Boolean {
        Logger.d("执行向下滑动")
        val success = swipeDownInternal()
        if (success) {
            Logger.d("向下滑动成功")
            // 滑动后延迟，等待界面稳定
            delay(1000)
        } else {
            Logger.w("向下滑动失败")
        }
        return success
    }

    /**
     * 向上滑动
     * @return 是否滑动成功
     */
    suspend fun scrollUp(): Boolean {
        Logger.d("执行向上滑动")
        val success = swipeUpInternal()
        if (success) {
            Logger.d("向上滑动成功")
            delay(1000)
        } else {
            Logger.w("向上滑动失败")
        }
        return success
    }

    /**
     * 向左滑动
     * @return 是否滑动成功
     */
    suspend fun scrollLeft(): Boolean {
        Logger.d("执行向左滑动")
        val success = swipeLeftInternal()
        if (success) {
            Logger.d("向左滑动成功")
            delay(1000)
        } else {
            Logger.w("向左滑动失败")
        }
        return success
    }

    /**
     * 向右滑动
     * @return 是否滑动成功
     */
    suspend fun scrollRight(): Boolean {
        Logger.d("执行向右滑动")
        val success = swipeRightInternal()
        if (success) {
            Logger.d("向右滑动成功")
            delay(1000)
        } else {
            Logger.w("向右滑动失败")
        }
        return success
    }

    /**
     * 点击节点
     * @param node 要点击的节点
     * @return 是否点击成功
     */
    suspend fun clickNode(node: AccessibilityNodeInfo?): Boolean {
        if (node == null) {
            Logger.e("节点为空，无法进行点击")
            return false
        }
        return try {
            val bounds = Rect()
            node.getBoundsInScreen(bounds)

            if (!bounds.isEmpty) {
                val centerX = bounds.centerX()
                val centerY = bounds.centerY()

                Logger.d("使用节点中心坐标点击 ($centerX, $centerY)")

                return clickByCoordinatesInternal(centerX.toFloat(), centerY.toFloat())
            } else {
                Logger.e("节点边界无效，无法进行坐标点击")
                return false
            }
        } catch (e: Exception) {
            Logger.e("点击节点异常: ${e.message}")
            false
        }
    }

    /**
     * 向指定节点输入文本
     * @param node 要输入文本的节点（通常是输入框）
     * @param text 要输入的文本内容
     * @return 是否输入成功
     */
    suspend fun inputText(node: AccessibilityNodeInfo?, text: String): Boolean {
        if (node == null) {
            Logger.e("节点为空，无法输入文本")
            return false
        }

        return try {
            Logger.d("准备向节点输入文本: $text")
            Logger.d("目标节点信息 - 类名: ${node.className}, 文本: ${node.text}, 描述: ${node.contentDescription}")

            // 方法1：尝试使用ACTION_SET_TEXT
            val arguments = Bundle()
            arguments.putCharSequence(
                AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE,
                text
            )

            val setTextSuccess =
                node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
            if (setTextSuccess) {
                Logger.i("使用ACTION_SET_TEXT输入文本成功: $text")
                delay(500) // 等待输入完成
                return true
            }

            Logger.w("ACTION_SET_TEXT失败，尝试其他方法")

            // 方法2：先聚焦节点，再尝试输入
            val focusSuccess = node.performAction(AccessibilityNodeInfo.ACTION_FOCUS)
            if (focusSuccess) {
                Logger.d("节点聚焦成功")
                delay(300)

                // 再次尝试设置文本
                val retrySetText =
                    node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
                if (retrySetText) {
                    Logger.i("聚焦后使用ACTION_SET_TEXT输入文本成功: $text")
                    delay(500)
                    return true
                }
            }

            Logger.w("聚焦后设置文本也失败，尝试点击后输入")

            // 方法3：先点击节点激活输入框，再尝试输入
            val clickSuccess = clickNode(node)
            if (clickSuccess) {
                Logger.d("节点点击成功，等待输入框激活")
                delay(500)

                // 获取当前聚焦的节点（可能是新的输入框节点）
                val service = getAppropriateService()
                if (service is SelectToSpeakService) {
                    val rootNode = service.getRootNode()
                    val focusedNode = findFocusedNode(rootNode)

                    if (focusedNode != null) {
                        Logger.d("找到聚焦的节点，尝试输入文本")
                        val focusedInputSuccess = focusedNode.performAction(
                            AccessibilityNodeInfo.ACTION_SET_TEXT,
                            arguments
                        )
                        if (focusedInputSuccess) {
                            Logger.i("在聚焦节点上输入文本成功: $text")
                            delay(500)
                            return true
                        }
                    }
                }

                // 如果找不到聚焦节点，在原节点上再试一次
                val finalTrySuccess =
                    node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
                if (finalTrySuccess) {
                    Logger.i("点击后在原节点输入文本成功: $text")
                    delay(500)
                    return true
                }
            }

            Logger.e("所有文本输入方法都失败了")
            false

        } catch (e: Exception) {
            Logger.e("输入文本时发生异常: $text", e)
            false
        }
    }

    /**
     * 查找当前聚焦的节点
     */
    private fun findFocusedNode(rootNode: AccessibilityNodeInfo?): AccessibilityNodeInfo? {
        if (rootNode == null) return null

        try {
            // 检查当前节点是否聚焦
            if (rootNode.isFocused) {
                return rootNode
            }

            // 递归查找子节点
            for (i in 0 until rootNode.childCount) {
                val child = rootNode.getChild(i)
                child?.let { childNode ->
                    val focusedChild = findFocusedNode(childNode)
                    if (focusedChild != null) {
                        return focusedChild
                    }
                }
            }

            return null
        } catch (e: Exception) {
            Logger.e("查找聚焦节点时发生异常", e)
            return null
        }
    }

    /**
     * 通过图像识别点击指定区域
     * @param templateImagePath 模板图像路径（相对于assets目录）
     * @param threshold 匹配阈值，默认0.8
     * @return 是否点击成功
     */
    suspend fun getImageRecognitionRect(
        templateImagePath: String,
        threshold: Double = 0.8
    ): ImageRecognitionHelper.TemplateMatchResult? {
        return try {
            Logger.d("开始图像识别点击: $templateImagePath")

            // 获取应用上下文
            val context = SuperNuggetsMasterApplication.instance
            if (context == null) {
                Logger.e("无法获取应用上下文")
                return null
            }

            // 创建图像识别助手和截图助手实例
            val imageRecognitionHelper = ImageRecognitionHelper(context)
            val screenshotHelper = ScreenshotHelper(context)

            // 同步OpenCV初始化状态（从已有实例获取状态）
            // 这里假设OpenCV已经在应用中初始化过了
            try {
                // 强制设置OpenCV已初始化状态，因为页面识别已经证明OpenCV工作正常
                imageRecognitionHelper.setOpenCVInitialized(true)
                Logger.d("已同步OpenCV初始化状态为true")
            } catch (e: Exception) {
                Logger.e("同步OpenCV状态失败", e)
                return null
            }

            // 获取当前屏幕截图
            val screenBitmap = screenshotHelper.captureScreen()
            if (screenBitmap == null) {
                Logger.e("截图失败，无法进行图像识别点击")
                return null
            }

            // 执行模板匹配并获取中心点坐标
            val templateResult =
                imageRecognitionHelper.findTemplate(screenBitmap, templateImagePath, threshold)

            // 释放截图资源
            screenBitmap.recycle()

            if (templateResult != null && templateResult.isMatch) {
                Logger.i("图像识别成功，置信度: ${templateResult.confidence}")
                return templateResult
            } else {
                val confidence = templateResult?.confidence ?: 0.0
                Logger.w("图像识别失败，未找到匹配的区域，置信度: $confidence")
                return null
            }

        } catch (e: Exception) {
            Logger.e("图像识别点击失败: $templateImagePath", e)
            null
        }
    }
}
