package com.jerome.supernuggetsmaster.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.view.WindowManager
import com.jerome.supernuggetsmaster.service.MediaProjectionService
import com.jerome.supernuggetsmaster.utils.Logger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * 基于Service的屏幕截图工具类
 *
 * 最终解决方案：
 * 1. 将MediaProjection管理完全交给MediaProjectionService
 * 2. ScreenshotHelper只负责调用Service的截图接口
 * 3. Service中维护长期有效的MediaProjection实例
 * 4. 每次截图在Service中创建新的VirtualDisplay
 */
@Singleton
class ScreenshotHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {

    /**
     * 获取屏幕截图
     * 直接调用MediaProjectionService的截图功能
     */
    suspend fun captureScreen(): Bitmap? {
        Logger.d("开始截图请求，调用Service")

        return try {
            val bitmap = MediaProjectionService.captureScreen()

            if (bitmap != null) {
                Logger.d("Service截图成功")
            } else {
                Logger.w("Service截图失败")
            }

            bitmap
        } catch (e: Exception) {
            Logger.e("调用Service截图时发生异常", e)
            null
        }
    }
    
    /**
     * 获取屏幕截图（兼容性方法）
     * 为了保持向后兼容性，提供takeScreenshot方法
     */
    suspend fun takeScreenshot(): Bitmap? {
        return captureScreen()
    }

    /**
     * 检查MediaProjection是否可用
     */
    fun isMediaProjectionAvailable(): Boolean {
        return MediaProjectionService.isRunning()
    }

    /**
     * 清理资源（兼容旧接口）
     */
    fun clearMediaProjection() {
        Logger.d("清理MediaProjection（通过Service）")
        // Service会自己管理MediaProjection的清理
    }
}
