package com.jerome.supernuggetsmaster.utils

import android.content.Context
import com.jerome.supernuggetsmaster.utils.Logger
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * OpenCV初始化管理器
 * 统一管理OpenCV的初始化过程
 */
@Singleton
class OpenCVInitializer @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private var isInitialized = false
    private var isInitializing = false
    private val pendingCallbacks = mutableListOf<(Boolean) -> Unit>()
    
    /**
     * 初始化OpenCV
     * 支持多个组件同时请求初始化，避免重复初始化
     */
    fun initialize(callback: (Boolean) -> Unit) {
        synchronized(this) {
            // 如果已经初始化完成，直接回调
            if (isInitialized) {
                callback(true)
                return
            }

            // 添加到待处理回调列表
            pendingCallbacks.add(callback)

            // 如果正在初始化，等待完成
            if (isInitializing) {
                return
            }

            // 开始初始化
            isInitializing = true
            Logger.i("开始初始化OpenCV")

            try {
                // 使用本地初始化OpenCV (推荐方式)
                val success = org.opencv.android.OpenCVLoader.initLocal()

                synchronized(this@OpenCVInitializer) {
                    if (success) {
                        Logger.i("OpenCV初始化成功")
                        isInitialized = true
                        isInitializing = false

                        // 通知所有等待的回调
                        pendingCallbacks.forEach { it(true) }
                        pendingCallbacks.clear()
                    } else {
                        Logger.e("OpenCV初始化失败")
                        isInitialized = false
                        isInitializing = false

                        // 通知所有等待的回调
                        pendingCallbacks.forEach { it(false) }
                        pendingCallbacks.clear()
                    }
                }
            } catch (e: Exception) {
                Logger.e("OpenCV初始化异常", e)
                synchronized(this@OpenCVInitializer) {
                    isInitialized = false
                    isInitializing = false

                    // 通知所有等待的回调
                    pendingCallbacks.forEach { it(false) }
                    pendingCallbacks.clear()
                }
            }
        }
    }
    
    /**
     * 检查OpenCV是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
}
