package com.jerome.supernuggetsmaster.float_window

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.graphics.Rect
import android.os.Build
import android.text.SpannableString
import android.text.Spanned
import android.text.style.BackgroundColorSpan
import android.view.*
import android.view.accessibility.AccessibilityNodeInfo
import android.view.accessibility.AccessibilityWindowInfo
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.core.content.ContextCompat
import com.jerome.supernuggetsmaster.R
import android.util.Log
import com.google.android.accessibility.selecttospeak.SelectToSpeakService
import java.lang.ref.WeakReference
import com.jerome.supernuggetsmaster.utils.Logger

class FloatDebugMenu(private val context: Context) {
    
    private var windowManager: WindowManager? = null
    private var menuView: View? = null
    private var layoutParams: WindowManager.LayoutParams? = null
    private var isShowing = false
    
    // UI组件
    private var etSearch: EditText? = null
    private var btnSearch: ImageButton? = null
    private var btnAnalyzeUI: Button? = null
    private var scrollNodeTree: ScrollView? = null
    private var containerNodeTree: LinearLayout? = null
    
    // 搜索结果相关
    private var layoutSearchResults: LinearLayout? = null
    private var containerSearchResults: LinearLayout? = null
    private var tvSearchResultTitle: TextView? = null
    private var btnBackToTree: ImageButton? = null
    
    // 数据
    private var rootNode: AccessibilityNodeData? = null
    private var searchResults: List<AccessibilityNodeData> = emptyList()
    private var currentSearchText = ""
    private var highlightedNode: AccessibilityNodeData? = null
    
    // 高亮覆盖层
    private var highlightOverlay: NodeHighlightOverlay? = null
    

    
    fun show() {
        if (isShowing) return
        
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        highlightOverlay = NodeHighlightOverlay(context)
        
        createMenuView()
        createLayoutParams()
        
        try {
            windowManager?.addView(menuView, layoutParams)
            isShowing = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    fun hide() {
        if (!isShowing || menuView == null || windowManager == null) return
        
        try {
            // 隐藏高亮覆盖层
            highlightOverlay?.hideHighlight()
            highlightOverlay = null
            
            windowManager?.removeView(menuView)
            isShowing = false
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    fun isShowing(): Boolean = isShowing
    
    @SuppressLint("InflateParams")
    private fun createMenuView() {
        val inflater = LayoutInflater.from(context)
        menuView = inflater.inflate(R.layout.float_debug_menu, null)
        
        initViews()
        setupListeners()
    }
    
    private fun initViews() {
        menuView?.let { view ->
            etSearch = view.findViewById(R.id.et_search)
            btnSearch = view.findViewById(R.id.btn_search)
            btnAnalyzeUI = view.findViewById(R.id.btn_analyze_ui)
            scrollNodeTree = view.findViewById(R.id.scroll_node_tree)
            containerNodeTree = view.findViewById(R.id.container_node_tree)
            
            // 搜索结果相关视图
            layoutSearchResults = view.findViewById(R.id.layout_search_results)
            containerSearchResults = view.findViewById(R.id.container_search_results)
            tvSearchResultTitle = view.findViewById(R.id.tv_search_result_title)
            btnBackToTree = view.findViewById(R.id.btn_back_to_tree)
            
            val btnClose = view.findViewById<ImageButton>(R.id.btn_close)
            btnClose.setOnClickListener { hide() }
        }
    }
    
    private fun setupListeners() {
        btnAnalyzeUI?.setOnClickListener {
            analyzeCurrentUI()
        }
        
        btnSearch?.setOnClickListener {
            performSearch()
        }
        
        etSearch?.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH || 
                actionId == android.view.inputmethod.EditorInfo.IME_ACTION_DONE) {
                performSearch()
                true
            } else {
                false
            }
        }
        
        // 输入框获得焦点时确保软键盘可以弹出
        etSearch?.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                etSearch?.post {
                    val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    imm.showSoftInput(etSearch, InputMethodManager.SHOW_IMPLICIT)
                }
            }
        }
        
        // 返回树视图按钮
        btnBackToTree?.setOnClickListener {
            showTreeView()
        }
    }
    
    private fun analyzeCurrentUI() {
        val accessibilityService = SelectToSpeakService.getInstance()
        if (accessibilityService == null) {
            showToast("无障碍服务未启用")
            return
        }
        
        // 先切换焦点到目标应用，然后再获取节点树
        switchFocusToTargetApp {
            getTargetAppNodeTree()
        }
    }
    
    /**
     * 切换焦点到目标应用
     */
    private fun switchFocusToTargetApp(callback: () -> Unit) {
        // 隐藏软键盘
        hideKeyboard()
        
        // 暂时将调试窗设为不可获得焦点
        layoutParams?.let { params ->
            params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            
            menuView?.let { view ->
                val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                try {
                    windowManager.updateViewLayout(view, params)
                    
                    // 延迟一下让系统重新分配焦点
                    view.postDelayed({
                        callback.invoke()
                        
                        // 恢复调试窗可获得焦点（为了输入框能正常工作）
                        params.flags = params.flags and WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE.inv()
                        try {
                            windowManager.updateViewLayout(view, params)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }, 200) // 200ms延迟
                    
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback.invoke()
                }
            }
        }
    }
    
    /**
     * 获取目标应用的节点树
     */
    private fun getTargetAppNodeTree() {
        val accessibilityService = SelectToSpeakService.getInstance()
        if (accessibilityService == null) {
            showToast("无障碍服务未启用")
            return
        }
        
        // 尝试获取所有窗口
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val windows = accessibilityService.windows
            var targetRootNode: AccessibilityNodeInfo? = null
            
            // 查找非调试窗口的根节点
            for (window in windows) {
                if (window.type == AccessibilityWindowInfo.TYPE_APPLICATION) {
                    val root = window.root
                    if (root != null && !isDebugWindow(root)) {
                        targetRootNode = root
                        break
                    }
                }
            }
            
            if (targetRootNode != null) {
                // 构建节点树
                rootNode = buildNodeTree(targetRootNode, 0)
                showTreeView()
                showToast("已获取目标应用界面信息")
                return
            }
        }
        
        // 后备方案：使用rootInActiveWindow
        val rootNodeInfo = accessibilityService.rootInActiveWindow
        if (rootNodeInfo == null) {
            showToast("无法获取当前界面信息，请确保目标应用在前台")
            return
        }
        
        // 检查是否仍然是调试窗口
        if (isDebugWindow(rootNodeInfo)) {
            showToast("请点击目标应用界面，让其获得焦点后再尝试")
            return
        }
        
        // 构建节点树
        rootNode = buildNodeTree(rootNodeInfo, 0)
        showTreeView()
        showToast("已获取目标应用界面信息")
    }
    
    /**
     * 判断是否是调试窗口
     */
    private fun isDebugWindow(rootNode: AccessibilityNodeInfo): Boolean {
        // 检查包名
        val packageName = rootNode.packageName?.toString()
        return packageName == context.packageName
    }
    

    
    private fun buildNodeTree(nodeInfo: AccessibilityNodeInfo, depth: Int): AccessibilityNodeData {
        val bounds = Rect()
        nodeInfo.getBoundsInScreen(bounds)
        
        val nodeData = AccessibilityNodeData(
            nodeInfo = nodeInfo,
            className = nodeInfo.className?.toString(),
            text = nodeInfo.text?.toString(),
            contentDescription = nodeInfo.contentDescription?.toString(),
            resourceId = nodeInfo.viewIdResourceName,
            bounds = bounds,
            isClickable = nodeInfo.isClickable,
            isScrollable = nodeInfo.isScrollable,
            isEnabled = nodeInfo.isEnabled,
            isVisible = nodeInfo.isVisibleToUser,
            depth = depth
        )
        
        // 递归构建子节点
        for (i in 0 until nodeInfo.childCount) {
            val child = nodeInfo.getChild(i)
            if (child != null) {
                val childData = buildNodeTree(child, depth + 1)
                nodeData.children.add(childData)
            }
        }
        
        return nodeData
    }
    
    private fun displayNodeTree() {
        containerNodeTree?.removeAllViews()
        rootNode?.let { root ->
            addNodeToView(root, containerNodeTree)
        }
    }
    
    private fun addNodeToView(nodeData: AccessibilityNodeData, container: LinearLayout?) {
        if (container == null) return
        
        // 创建节点视图
        val nodeView = createNodeView(nodeData)
        container.addView(nodeView)
        
        // 如果节点展开，显示子节点
        if (nodeData.isExpanded && nodeData.children.isNotEmpty()) {
            nodeData.children.forEach { child ->
                addNodeToView(child, container)
            }
        }
    }
    
    @SuppressLint("InflateParams")
    private fun createNodeView(nodeData: AccessibilityNodeData): View {
        val inflater = LayoutInflater.from(context)
        val itemView = inflater.inflate(R.layout.item_node_tree, null)
        
        val ivExpand = itemView.findViewById<ImageView>(R.id.iv_expand)
        val tvNodeText = itemView.findViewById<TextView>(R.id.tv_node_text)
        val btnLocate = itemView.findViewById<ImageButton>(R.id.btn_locate)
        
        // 保存节点数据作为tag，用于后续查找
        itemView.tag = nodeData
        
        // 设置缩进
        val indentSize = nodeData.depth * 20
        itemView.setPadding(indentSize, 4, 4, 4)
        
        // 设置展开图标
        if (nodeData.children.isNotEmpty()) {
            ivExpand.visibility = View.VISIBLE
            ivExpand.rotation = if (nodeData.isExpanded) 90f else 0f
        } else {
            ivExpand.visibility = View.GONE
        }
        
        // 设置节点文本
        val displayText = nodeData.getDisplayText()
        if (currentSearchText.isNotBlank() && nodeData.containsText(currentSearchText)) {
            tvNodeText.text = highlightSearchText(displayText, currentSearchText)
        } else {
            tvNodeText.text = displayText
        }
        
        // 检查是否为高亮节点
        if (nodeData == highlightedNode) {
            itemView.setBackgroundColor(ContextCompat.getColor(context, R.color.search_highlight))
            // 2秒后清除高亮
            itemView.postDelayed({
                itemView.setBackgroundColor(android.graphics.Color.TRANSPARENT)
                highlightedNode = null
            }, 2000)
        } else {
            itemView.setBackgroundColor(android.graphics.Color.TRANSPARENT)
        }
        
        // 设置展开/折叠点击事件
        itemView.setOnClickListener {
            if (nodeData.children.isNotEmpty()) {
                nodeData.isExpanded = !nodeData.isExpanded
                displayNodeTree()
            }
        }
        
        // 设置定位按钮点击事件
        btnLocate.setOnClickListener {
            nodeData.bounds?.let { bounds ->
                highlightOverlay?.showHighlight(bounds)
                logNodeInfo(nodeData, "树视图节点定位")
                showToast("已定位节点位置")
            } ?: showToast("节点无位置信息")
        }
        
        return itemView
    }
    
    private fun performSearch() {
        val searchText = etSearch?.text?.toString()?.trim() ?: ""
        if (searchText.isBlank()) {
            currentSearchText = ""
            showTreeView()
            return
        }
        
        currentSearchText = searchText
        
        rootNode?.let { root ->
            searchResults = root.findNodesContaining(searchText)
            
            if (searchResults.isNotEmpty()) {
                showSearchResults()
                showToast("找到 ${searchResults.size} 个匹配结果")
            } else {
                showToast("未找到匹配的节点")
            }
        }
        
        // 隐藏软键盘
        hideKeyboard()
    }
    
    private fun highlightSearchText(text: String, searchText: String): SpannableString {
        val spannableString = SpannableString(text)
        val lowercaseText = text.lowercase()
        val lowercaseSearch = searchText.lowercase()
        
        var startIndex = 0
        while (true) {
            val index = lowercaseText.indexOf(lowercaseSearch, startIndex)
            if (index == -1) break
            
            val highlightSpan = BackgroundColorSpan(ContextCompat.getColor(context, R.color.search_highlight))
            spannableString.setSpan(highlightSpan, index, index + searchText.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            
            startIndex = index + searchText.length
        }
        
        return spannableString
    }
    
    private fun hideKeyboard() {
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        etSearch?.let { editText ->
            imm.hideSoftInputFromWindow(editText.windowToken, 0)
            editText.clearFocus()
        }
    }
    
    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 显示树视图
     */
    private fun showTreeView() {
        scrollNodeTree?.visibility = View.VISIBLE
        layoutSearchResults?.visibility = View.GONE
        displayNodeTree()
    }
    
    /**
     * 跳转到树视图并定位到指定节点
     */
    private fun jumpToNodeInTree(targetNode: AccessibilityNodeData) {
        // 清除之前的高亮
        highlightedNode = null
        
        // 展开到目标节点的路径
        rootNode?.let { root ->
            // 展开路径
            expandPathToNode(root, targetNode)
            
            // 设置高亮节点
            highlightedNode = targetNode
            
            // 切换到树视图
            showTreeView()
            
            // 延迟滚动到目标节点（让视图先更新）
            containerNodeTree?.post {
                scrollToHighlightedNode()
            }
        }
    }
    
    /**
     * 展开到目标节点的路径
     */
    private fun expandPathToNode(current: AccessibilityNodeData, target: AccessibilityNodeData): Boolean {
        if (current == target) {
            return true
        }
        
        for (child in current.children) {
            if (expandPathToNode(child, target)) {
                current.isExpanded = true
                return true
            }
        }
        
        return false
    }
    
    /**
     * 滚动到高亮节点位置
     */
    private fun scrollToHighlightedNode() {
        highlightedNode?.let { target ->
            val container = containerNodeTree ?: return
            val scrollView = scrollNodeTree ?: return
            
            // 延迟执行，确保layout完成
            container.post {
                // 查找目标节点视图
                var targetView: View? = null
                
                for (i in 0 until container.childCount) {
                    val childView = container.getChildAt(i)
                    val nodeData = childView.tag as? AccessibilityNodeData
                    
                    if (nodeData == target) {
                        targetView = childView
                        break
                    }
                }
                
                targetView?.let { view ->
                    // 获取view在父容器中的位置
                    val viewTop = view.top
                    val viewHeight = view.height
                    val scrollViewHeight = scrollView.height
                    
                    // 计算滚动位置，让目标view显示在可视区域中央
                    val targetScrollY = viewTop - (scrollViewHeight - viewHeight) / 2
                    
                    // 平滑滚动到目标位置
                    scrollView.smoothScrollTo(0, maxOf(0, targetScrollY))
                }
            }
        }
    }
    

    
    /**
     * 显示搜索结果列表
     */
    private fun showSearchResults() {
        scrollNodeTree?.visibility = View.GONE
        layoutSearchResults?.visibility = View.VISIBLE
        displaySearchResults()
    }
    
    /**
     * 显示搜索结果列表
     */
    private fun displaySearchResults() {
        containerSearchResults?.removeAllViews()
        
        // 更新标题
        tvSearchResultTitle?.text = "搜索结果 (${searchResults.size})"
        
        searchResults.forEachIndexed { index, nodeData ->
            val resultView = createSearchResultItem(nodeData, index + 1)
            containerSearchResults?.addView(resultView)
        }
    }
    
    /**
     * 创建搜索结果项
     */
    @SuppressLint("InflateParams")
    private fun createSearchResultItem(nodeData: AccessibilityNodeData, index: Int): View {
        val inflater = LayoutInflater.from(context)
        val itemView = inflater.inflate(R.layout.item_node_tree, null)
        
        val ivExpand = itemView.findViewById<ImageView>(R.id.iv_expand)
        val tvNodeText = itemView.findViewById<TextView>(R.id.tv_node_text)
        val btnLocate = itemView.findViewById<ImageButton>(R.id.btn_locate)
        
        // 隐藏展开图标（搜索结果不需要展开）
        ivExpand.visibility = View.GONE
        
        // 设置编号和缩进
        itemView.setPadding(8, 4, 4, 4)
        
        // 设置节点文本，包含编号
        val displayText = "$index. ${nodeData.getDisplayText()}"
        val highlightedText = highlightSearchText(displayText, currentSearchText)
        tvNodeText.text = highlightedText
        
        // 设置定位按钮点击事件
        btnLocate.setOnClickListener {
            // 高亮显示节点
            nodeData.bounds?.let { bounds ->
                highlightOverlay?.showHighlight(bounds)
            }
            
            // 输出日志
            logNodeInfo(nodeData, "搜索结果 #${index} 定位按钮")
            
            // 跳转到树视图并展开到目标节点
            jumpToNodeInTree(nodeData)
            
            showToast("已定位到树视图中的第${index}个节点")
        }
        
        // 设置点击事件，跳转到树视图并定位节点
        itemView.setOnClickListener {
            // 高亮显示节点
            nodeData.bounds?.let { bounds ->
                highlightOverlay?.showHighlight(bounds)
            }
            
            // 输出日志
            logNodeInfo(nodeData, "搜索结果 #${index} 跳转到树视图")
            
            // 跳转到树视图并展开到目标节点
            jumpToNodeInTree(nodeData)
            
            showToast("已定位到树视图中的节点")
        }
        
        return itemView
    }
    
    /**
     * 显示节点详细信息
     */
    private fun showNodeDetail(nodeData: AccessibilityNodeData, index: Int) {
        val details = buildString {
            append("节点 #$index 详细信息:\n\n")
            append("类名: ${nodeData.className ?: "无"}\n")
            append("文本: ${nodeData.text ?: "无"}\n")
            append("描述: ${nodeData.contentDescription ?: "无"}\n")
            append("资源ID: ${nodeData.resourceId ?: "无"}\n")
            nodeData.bounds?.let { bounds ->
                append("位置: (${bounds.left}, ${bounds.top})\n")
                append("尺寸: ${bounds.width()} × ${bounds.height()}\n")
            }
            append("可点击: ${if (nodeData.isClickable) "是" else "否"}\n")
            append("可滚动: ${if (nodeData.isScrollable) "是" else "否"}\n")
            append("已启用: ${if (nodeData.isEnabled) "是" else "否"}\n")
            append("可见: ${if (nodeData.isVisible) "是" else "否"}")
        }
        showToast(details)
    }
    
    /**
     * 输出节点信息到控制台
     */
    private fun logNodeInfo(nodeData: AccessibilityNodeData, action: String) {
        Logger.d("==================== $action ====================")
        Logger.d("节点类名: ${nodeData.className ?: "无"}")
        Logger.d("节点文本: ${nodeData.text ?: "无"}")
        Logger.d("内容描述: ${nodeData.contentDescription ?: "无"}")
        Logger.d("资源ID: ${nodeData.resourceId ?: "无"}")
        
        // 位置信息
        nodeData.bounds?.let { bounds ->
            Logger.d("位置坐标: (${bounds.left}, ${bounds.top}) - (${bounds.right}, ${bounds.bottom})")
            Logger.d("节点尺寸: ${bounds.width()} × ${bounds.height()}")
            Logger.d("中心点: (${bounds.centerX()}, ${bounds.centerY()})")
        } ?: Logger.d("位置信息: 无")
        
        Logger.d("节点深度: ${nodeData.depth}")
        Logger.d("子节点数: ${nodeData.children.size}")
        Logger.d("可点击: ${nodeData.isClickable}")
        Logger.d("可滚动: ${nodeData.isScrollable}")
        Logger.d("已启用: ${nodeData.isEnabled}")
        Logger.d("用户可见: ${nodeData.isVisible}")
        Logger.d("是否展开: ${nodeData.isExpanded}")
        
        // 子节点预览
        if (nodeData.children.isNotEmpty()) {
            Logger.d("子节点预览:")
            nodeData.children.take(3).forEachIndexed { index, child ->
                val childText = child.text ?: child.contentDescription ?: child.className ?: "未知节点"
                Logger.d("  - 子节点${index + 1}: $childText")
            }
            if (nodeData.children.size > 3) {
                Logger.d("  - 还有${nodeData.children.size - 3}个子节点...")
            }
        }
        
        Logger.d("=========================================================")
    }
    
    private fun createLayoutParams(): WindowManager.LayoutParams {
        val windowType = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }
        
        layoutParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            windowType,
            // 移除FLAG_NOT_FOCUSABLE以允许软键盘弹出
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.CENTER
            // 允许调整窗口大小以适应软键盘
            softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN
        }
        
        return layoutParams!!
    }
} 