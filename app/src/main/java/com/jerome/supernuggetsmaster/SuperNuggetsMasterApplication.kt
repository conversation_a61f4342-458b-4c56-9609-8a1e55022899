package com.jerome.supernuggetsmaster

import android.app.Application
import com.jerome.supernuggetsmaster.utils.Logger
import dagger.hilt.android.HiltAndroidApp

/**
 * SuperNuggets Master 应用主类
 * 
 * 职责：
 * 1. 初始化 Hilt 依赖注入
 * 2. 全局应用配置
 * 3. 应用生命周期管理
 * 4. 日志系统初始化
 */
@HiltAndroidApp
class SuperNuggetsMasterApplication : Application() {

    companion object {
        @Volatile
        var instance: SuperNuggetsMasterApplication? = null
            private set
    }

    override fun onCreate() {
        super.onCreate()
        
        // 设置全局实例
        instance = this
        
        // 应用初始化逻辑
        initializeApp()
    }

    private fun initializeApp() {
        // 初始化日志系统
        try {
            // 设置Logger的Context引用
            Logger.setContext(this)
            
            // 记录应用启动日志
            Logger.i("SuperNuggets Master 应用启动")
            Logger.i("日志系统已初始化，支持悬浮窗日志显示")
        } catch (e: Exception) {
            // 如果Logger初始化失败，使用系统Log记录
            android.util.Log.e("SuperNuggets-Application", "Logger初始化失败", e)
        }
    }
} 