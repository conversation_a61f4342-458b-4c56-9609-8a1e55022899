# SuperNuggetsMaster - Android自动化脚本引擎

## 项目简介

SuperNuggetsMaster是一个基于Android无障碍服务的自动化脚本引擎，专门用于在移动设备上执行自动化任务。该项目使用现代化的Android开发技术栈，包括Kotlin、Jetpack Compose和MVVM架构。

## 核心功能

### 🎯 自动化脚本执行
- **微信自动化**: 支持微信应用的自动化操作
- **支付宝自动化**: 提供支付宝应用的自动化功能
- **快手自动化**: 实现快手应用的自动化脚本

### 🔍 智能识别系统
- **页面识别**: 基于OpenCV的图像识别技术，自动识别当前页面状态
- **截图分析**: 实时截取屏幕并进行智能分析
- **应用启动器**: 智能启动目标应用并导航到指定页面

### 🛡️ 权限管理
- **无障碍服务**: 核心的自动化执行服务
- **媒体投影**: 屏幕截图和录制功能
- **悬浮窗权限**: 调试和监控界面显示

## 技术架构

### 🏗️ 架构模式
- **MVVM**: Model-View-ViewModel架构模式
- **依赖注入**: 使用Hilt进行依赖管理
- **响应式编程**: Kotlin Flow和Coroutines

### 🔧 核心组件

#### 引擎层 (Engine)
- `ScriptEngine`: 脚本执行引擎核心
- `PageRecognizer`: 页面识别器
- `AppLauncher`: 应用启动器

#### 服务层 (Service)
- `AutomationAccessibilityService`: 无障碍服务，提供自动化操作能力
- `MediaProjectionService`: 媒体投影服务，处理屏幕截图和录制
- `ServiceLifecycleOwner`: 服务生命周期管理

#### 脚本层 (Scripts)
- `AlipayScript`: 支付宝自动化脚本
- `WeChatScript`: 微信自动化脚本
- `KuaishouScript`: 快手自动化脚本

#### 工具层 (Utils)
- `ImageRecognitionHelper`: 图像识别助手
- `ScreenshotHelper`: 截图助手
- `MediaProjectionHelper`: 媒体投影助手
- `PermissionManager`: 权限管理器
- `Logger`: 日志管理器
- `AssistHelper`: 自动化辅助工具类，提供简洁的API封装无障碍服务操作
  - **节点查找功能**: 支持单个和批量节点查找
    - `findByDesc(desc)`: 通过内容描述查找第一个匹配的节点
    - `findAllByDesc(desc)`: 通过内容描述查找所有匹配的节点
    - `findByText(text)`: 通过文本内容查找第一个匹配的节点
    - `findAllByText(text)`: 通过文本内容查找所有匹配的节点
    - `findById(id)`: 通过资源ID查找节点集合
  - **智能服务选择**: 针对微信8.0.52+版本节点混淆问题的解决方案
  - **坐标操作**: 点击、长按、滑动等手势操作
  - **文本输入**: 智能文本输入，支持多种输入方式

#### 调试层 (Float Window)
- `FloatWindowManager`: 悬浮窗管理器，提供可拖动的调试界面
  - 可爱的小机器人图标，友好的视觉设计
  - 悬浮窗大小为屏幕宽度的1/6，智能适配设备尺寸
  - 使用WeakReference避免内存泄漏
  - 自动资源清理和生命周期管理
  - 安全的Context引用处理和边界检测
- `FloatDebugMenu`: 展开式调试菜单，提供界面分析功能
  - 实时获取无障碍节点树结构
  - 可折叠/展开的层级节点显示
  - 智能焦点切换，确保获取正确的目标应用节点
  - 搜索结果列表显示，支持快速定位
  - 支持软键盘输入的搜索框
  - 每个节点的定位按钮功能
  - 搜索结果跳转到树视图功能
  - 智能滚动和节点高亮显示
  - 控制台日志输出调试信息
- `AccessibilityNodeData`: 节点数据模型，封装节点信息和操作
- `NodeHighlightOverlay`: 节点高亮覆盖层，在屏幕上显示节点边界
  - 橙红色矩形边框标识
  - 半透明背景填充
  - 四角L形标记和中心点
  - 显示节点尺寸和坐标信息
  - 精确的坐标定位（已修复状态栏偏移）
  - 自动3秒后消失

### 🎨 用户界面
- **Jetpack Compose**: 现代化的UI框架
- **Material Design 3**: 遵循最新设计规范
- **响应式设计**: 适配不同尺寸设备

## 项目结构

```
app/src/main/java/com/jerome/supernuggetsmaster/
├── annotation/          # 注解定义
├── data/               # 数据模型
├── di/                 # 依赖注入模块
├── engine/             # 核心引擎
├── float_window/       # 悬浮窗调试工具
│   ├── FloatWindowManager.kt      # 悬浮窗管理器
│   ├── FloatDebugMenu.kt          # 调试菜单界面
│   ├── AccessibilityNodeData.kt   # 节点数据模型
│   └── NodeHighlightOverlay.kt    # 节点高亮覆盖层
├── permission/         # 权限管理
├── scripts/            # 自动化脚本
├── service/            # 系统服务
├── ui/                 # 用户界面
├── utils/              # 工具类
└── MainActivity.kt     # 主活动
```

## 使用方法

### 1. 权限设置
启动应用后，需要授予以下权限：
- **无障碍服务权限**: 用于执行自动化操作
- **悬浮窗权限**: 用于显示调试界面
- **媒体投影权限**: 用于屏幕截图

### 2. 脚本执行
1. 选择要执行的脚本类型（微信/支付宝/快手）
2. 配置脚本参数
3. 启动脚本执行
4. 监控执行状态和日志

### 3. 调试模式
应用提供完整的调试功能：
- **悬浮窗调试**: 点击右上角调试按钮显示可拖动的悬浮窗调试工具
  - 悬浮窗大小为屏幕宽度的1/6，自动适应不同设备
  - 使用可爱的小机器人图标，直观友好的设计
  - **界面分析菜单**: 点击机器人图标展开调试菜单
    - **界面分析**: 获取当前应用的完整节点树结构
    - **节点展开**: 点击节点可层层展开查看子节点
    - **节点定位**: 每个节点右侧有定位按钮，点击后在屏幕上显示橙红色矩形边框标识节点位置（已修复状态栏偏移问题）
    - **控制台日志**: 点击定位按钮时自动输出节点详细信息到控制台，便于开发调试
    - **智能搜索**: 输入文本快速定位包含该文本的节点，支持软键盘输入
    - **搜索结果列表**: 搜索后显示专门的结果列表，包含编号和高亮显示
    - **节点跳转定位**: 点击搜索结果可跳转到树视图并自动展开到对应节点位置
    - **智能滚动**: 跳转后自动滚动到目标节点并高亮显示2秒
    - **父子关系查看**: 通过跳转功能可以直观看到节点在整个UI树中的层级关系
    - **视图切换**: 支持在树状视图和搜索结果列表之间切换
- **实时日志查看**: 查看应用运行状态和错误信息
- **屏幕截图预览**: 预览当前截图和识别结果
- **页面识别结果**: 显示图像识别的详细信息
- **执行状态监控**: 监控脚本执行进度和状态

## 技术特色

### 🚀 性能优化
- **内存管理**: 避免内存泄漏的最佳实践，使用WeakReference管理悬浮窗引用
- **生命周期管理**: 正确处理Android组件生命周期，在Activity销毁时清理资源
- **异步操作**: 使用Coroutines进行非阻塞操作
- **资源清理**: 自动清理悬浮窗相关的Context引用，防止内存泄漏

### 🔒 安全措施
- **权限控制**: 严格的权限管理机制
- **数据保护**: 敏感数据的安全处理
- **隐私保护**: 遵循Android隐私保护指南

### 📱 兼容性
- **多版本支持**: 兼容不同Android版本
- **设备适配**: 支持不同屏幕尺寸和分辨率
- **性能适配**: 针对不同性能设备优化

## 开发环境

- **Kotlin**: 主要编程语言
- **Android SDK**: 目标API Level 34
- **Jetpack Compose**: UI框架
- **OpenCV**: 图像处理库
- **Hilt**: 依赖注入框架
- **Room**: 本地数据库（如需要）

## 注意事项

⚠️ **重要提醒**：
1. 本项目仅用于学习和研究目的
2. 使用自动化功能时请遵守相关应用的服务条款
3. 请勿用于任何违法或有害的活动
4. 确保在使用前获得适当的权限和授权

## 版本更新

### v1.0.0
- 初始版本发布
- 核心自动化引擎实现
- 基础脚本支持
- 权限管理系统

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: GitHub Issues页面
- 开发者: jerome

---

*本项目基于Android开发最佳实践构建，遵循Material Design设计规范* 